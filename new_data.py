"""
Simple backend script to generate company-specific CSV files with user data.
Uses faker library for user data generation and GPT-4o-mini for user behavior content.
"""

import pandas as pd
import json
import random
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from faker import Faker
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize faker
fake = Faker()

def load_product_details():
    """Load product details from the data file"""
    try:
        with open('data/product_details.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Product details file not found. Using default products.")
        return [
            {
                "Product_Name": "GenAI Pinnacle Plus Program",
                "Company_Name": "Analytics Vidhya",
                "Product_Features": ["AI/ML Training", "Hands-on Projects", "Industry Mentorship"]
            },
            {
                "Product_Name": "Agentic AI Pioneer Program", 
                "Company_Name": "Analytics Vidhya",
                "Product_Features": ["Agentic AI", "Advanced Algorithms", "Real-world Applications"]
            }
        ]

def load_organization_data():
    """Load organization data to get company information"""
    # First try to get from command line argument (passed from UI)
    import sys
    if len(sys.argv) > 1:
        try:
            # Parse the organization URL from command line
            org_url = sys.argv[1]
            with open('data/organization_data.json', 'r') as f:
                org_data = json.load(f)

                if org_url in org_data:
                    data = org_data[org_url]
                    # Extract company name from URL
                    if 'amazon' in org_url.lower():
                        company_name = "Amazon"
                    elif 'analyticsvidhya' in org_url.lower():
                        company_name = "Analytics Vidhya"
                    else:
                        # Extract from domain
                        domain = org_url.replace('https://', '').replace('http://', '').replace('www.', '').split('/')[0]
                        company_name = domain.split('.')[0].title()

                    return {
                        "company_name": company_name,
                        "domain": data.get('Domain', 'Unknown'),
                        "url": org_url
                    }
        except:
            pass

    # Fallback to finding saved organization
    try:
        with open('data/organization_data.json', 'r') as f:
            org_data = json.load(f)

            # Find the organization with saved=true
            for url, data in org_data.items():
                if data.get('saved', False):
                    # Extract company name from URL
                    if 'amazon' in url.lower():
                        company_name = "Amazon"
                    elif 'analyticsvidhya' in url.lower():
                        company_name = "Analytics Vidhya"
                    else:
                        # Extract from domain
                        domain = url.replace('https://', '').replace('http://', '').replace('www.', '').split('/')[0]
                        company_name = domain.split('.')[0].title()

                    return {
                        "company_name": company_name,
                        "domain": data.get('Domain', 'Unknown'),
                        "url": url
                    }

            # Fallback if no saved organization found
            return {
                "company_name": "Analytics Vidhya",
                "domain": "analyticsvidhya.com"
            }
    except FileNotFoundError:
        print("Organization data file not found. Using default company.")
        return {
            "company_name": "Analytics Vidhya",
            "domain": "analyticsvidhya.com"
        }

def generate_random_datetime_last_week():
    """Generate random datetime from last week (without minutes/seconds)"""
    today = datetime.now()
    last_week_start = today - timedelta(days=7)
    last_week_end = today - timedelta(days=1)
    
    # Random day between last week start and end
    random_days = random.randint(0, 6)
    random_date = last_week_start + timedelta(days=random_days)
    
    # Random hour (0-23)
    random_hour = random.randint(0, 23)
    
    # Set minutes and seconds to 0
    return random_date.replace(hour=random_hour, minute=0, second=0, microsecond=0)

def generate_send_time(open_time):
    """Generate send time (day-1 from open_time or random if open_time is NaN)"""
    if pd.isna(open_time):
        return generate_random_datetime_last_week()
    else:
        # Day before open_time
        return open_time - timedelta(days=1)

def generate_user_data(company_name, num_users=100):
    """Generate user data for a specific company"""

    # Load product details
    products = load_product_details()

    # Filter products for the company or use all if none match
    company_products = [p for p in products if p.get('Company_Name', '').lower() == company_name.lower()]
    if not company_products:
        company_products = products

    # Load user journey data to get product-specific stages
    user_journey_data = {}
    try:
        with open('data/user_journey.json', 'r') as f:
            user_journey_data = json.load(f)
    except Exception as e:
        print(f"Warning: Could not load user_journey.json: {e}")

    # Engagement statuses
    engagement_statuses = ["High", "Medium", "Low", "Inactive"]

    users_data = []

    for i in range(num_users):
        # Generate basic user info
        first_name = fake.first_name()
        email = f"{first_name.lower()}@{company_name.lower().replace(' ', '')}.com"

        # Random selections
        engagement_status = random.choice(engagement_statuses)
        selected_product = random.choice(company_products)
        last_product_sent = selected_product['Product_Name']

        # Get stages for the selected product from user_journey.json
        product_stages = user_journey_data.get(last_product_sent, user_journey_data.get('default', []))

        if product_stages:
            # Extract only current_stage names from the journey data
            available_stages = [stage['current_stage'] for stage in product_stages]
            # Remove duplicates and filter out None values
            available_stages = list(set([stage for stage in available_stages if stage]))
            user_stage = random.choice(available_stages[:-1])
        else:
            # Fallback to default stages if no product-specific stages found
            default_stages = [
                "New Visitor", "Product Viewed", "Lead Generated",
                "Trial Started", "Purchase Intent"
            ]
            user_stage = random.choice(default_stages)
        
        # Generate random times
        # 70% chance of having open/click times, 30% NaN
        if random.random() < 0.7:
            last_open_time = generate_random_datetime_last_week()
            # 80% chance of having click time if open time exists
            if random.random() < 0.8:
                # Click time should be after or same as open time
                last_click_time = last_open_time + timedelta(hours=random.randint(0, 24))
            else:
                last_click_time = None
        else:
            last_open_time = None
            last_click_time = None
        
        send_time = generate_send_time(last_open_time)
        
        user_data = {
            'user_email': email,
            'first_name': first_name,
            'user_stage': user_stage,
            'engagement_status': engagement_status,
            'last_product_sent': last_product_sent,
            'Last_Open_Time': last_open_time.strftime('%Y-%m-%d %H:%M:%S') if last_open_time else None,
            'Last_Click_Time': last_click_time.strftime('%Y-%m-%d %H:%M:%S') if last_click_time else None,
            'Send_Time': send_time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        users_data.append(user_data)
    
    return users_data

async def generate_user_behavior(users_data, company_products):
    """Generate user behavior content using GPT-4o-mini"""
    
    # Initialize LLM
    llm_model = "gpt-4o-mini-2024-07-18"
    api_key = os.getenv("OPENAI_API_KEY")
    
    if not api_key:
        print("Warning: OpenAI API key not found. Using placeholder behavior.")
        # Add placeholder behavior for each user
        for user in users_data:
            user['user_behaviour'] = f"{user['first_name']} is interested in {user['last_product_sent']} and has {user['engagement_status'].lower()} engagement with our content."
        return users_data
    
    llm = ChatOpenAI(temperature=0.9, model=llm_model, api_key=api_key)
    
    # Create DataFrame for processing
    df = pd.DataFrame(users_data)
    
    # Prepare product information for the prompt
    products_info = "\n".join([
        f"- {p['Product_Name']}: {', '.join(p.get('Product_Features', []))}" 
        for p in company_products
    ])
    
    # Create prompt template
    products_prompt = """
    You are generating realistic user behavior descriptions for marketing purposes.
    
    Available Products:
    {products_info}
    
    User Information:
    - Name: {first_name}
    - Email: {user_email}
    - Current Stage: {user_stage}
    - Engagement Level: {engagement_status}
    - Last Product Sent: {last_product_sent}
    - Last Open Time: {Last_Open_Time}
    - Last Click Time: {Last_Click_Time}
    - Send Time: {Send_Time}
    
    Generate a realistic user behavior description (2-4 sentences) on your own that may include:
    1. User's background/location (make it diverse)
    2. Content they've been browsing (may or may not be related to any of the products)
    3. Their engagement pattern based on the timing data
    4. You can mention things like Visited Product Page or Opened last mail, but not always. 
    
    Return ONLY a JSON object with this exact structure:
    {{
        "user_behaviour": "description here"
    }}
    """
    
    prompt_template = ChatPromptTemplate.from_template(products_prompt)
    
    # Prepare input list
    input_list = []
    for _, row in df.iterrows():
        input_data = row.to_dict()
        input_data['products_info'] = products_info
        input_list.append(input_data)
    
    # Create chain
    chain = (prompt_template | llm)
    
    try:
        # Process in batches to avoid rate limits
        batch_size = 10
        results = []
        
        for i in range(0, len(input_list), batch_size):
            batch = input_list[i:i+batch_size]
            print(f"Processing batch {i//batch_size + 1}/{(len(input_list)-1)//batch_size + 1}")
            
            batch_results = await chain.map().ainvoke(batch)
            results.extend(batch_results)
            
            # Small delay between batches
            await asyncio.sleep(1)
        
        # Parse results and add to user data
        for i, result in enumerate(results):
            try:
                # Parse JSON response
                if hasattr(result, 'content'):
                    content = result.content
                else:
                    content = str(result)
                
                # Extract JSON from response
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    behavior_data = json.loads(json_match.group())
                    users_data[i]['user_behaviour'] = behavior_data.get('user_behaviour', f"User interested in {users_data[i]['last_product_sent']}")
                else:
                    users_data[i]['user_behaviour'] = f"User interested in {users_data[i]['last_product_sent']}"
                    
            except Exception as e:
                print(f"Error parsing result for user {i}: {e}")
                users_data[i]['user_behaviour'] = f"User interested in {users_data[i]['last_product_sent']}"
    
    except Exception as e:
        print(f"Error generating user behavior: {e}")
        # Fallback to simple behavior descriptions
        for user in users_data:
            user['user_behaviour'] = f"{user['first_name']} is interested in {user['last_product_sent']} and has {user['engagement_status'].lower()} engagement with our content."
    
    return users_data

def save_company_csv(company_name, users_data):
    """Save user data to company-specific CSV file"""
    
    # Create directory if it doesn't exist
    os.makedirs('Sample Data For Mass Generation', exist_ok=True)
    
    # Create filename
    safe_company_name = company_name.replace(' ', '_').replace('/', '_')
    filename = f'Sample Data For Mass Generation/{safe_company_name}_processed_user_data.csv'
    
    # Convert to DataFrame
    df = pd.DataFrame(users_data)
    
    # Reorder columns to match the expected format
    column_order = [
        'user_email', 'first_name', 'user_behaviour', 'user_stage', 
        'engagement_status', 'last_product_sent', 'Last_Open_Time', 
        'Last_Click_Time', 'Send_Time'
    ]
    
    # Ensure all columns exist
    for col in column_order:
        if col not in df.columns:
            df[col] = None
    
    df = df[column_order]
    
    # Save to CSV
    df.to_csv(filename, index=False)
    print(f"Generated {filename} with {len(users_data)} users")
    
    return filename

async def main():
    """Main function to generate company-specific user data"""
    
    # Load organization data to get company name
    org_data = load_organization_data()
    company_name = org_data.get('company_name', 'Analytics Vidhya')
    
    print(f"Generating user data for company: {company_name}")
    
    # Load products for the company
    products = load_product_details()
    company_products = [p for p in products if p.get('Company_Name', '').lower() == company_name.lower()]
    if not company_products:
        company_products = products
    
    print(f"Found {len(company_products)} products for {company_name}")
    
    # Generate user data
    print("Generating basic user data...")
    users_data = generate_user_data(company_name, num_users=50)  # Start with 50 users
    
    # Generate user behavior using LLM
    print("Generating user behavior descriptions...")
    users_data = await generate_user_behavior(users_data, company_products)
    
    # Save to CSV
    filename = save_company_csv(company_name, users_data)
    
    print(f"Successfully generated {filename}")
    print("Sample data:")
    df = pd.DataFrame(users_data)
    print(df.head())

if __name__ == "__main__":
    asyncio.run(main())
